<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Landing Form</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            max-width: 500px;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            opacity: 0.9;
        }
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        .features {
            margin-top: 3rem;
            text-align: left;
        }
        .feature {
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        .feature::before {
            content: "✓";
            margin-right: 0.5rem;
            color: #4ade80;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>You're Offline</h1>
        <p>
            Don't worry! This app works offline too. Your form data will be saved locally 
            and submitted automatically when you're back online.
        </p>
        
        <a href="/" class="retry-btn" onclick="window.location.reload()">
            Try Again
        </a>
        
        <div class="features">
            <div class="feature">Form data saved locally</div>
            <div class="feature">Auto-submit when online</div>
            <div class="feature">Works without internet</div>
        </div>
    </div>

    <script>
        // Check if we're back online
        window.addEventListener('online', function() {
            window.location.reload();
        });
        
        // Show connection status
        function updateConnectionStatus() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }
        
        // Check connection every 5 seconds
        setInterval(updateConnectionStatus, 5000);
    </script>
</body>
</html>
