import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

const LocationMarker = ({ position, setPosition }) => {
  useMapEvents({
    click(e) {
      setPosition(e.latlng);
    },
  });

  return position === null ? null : (
    <Marker position={position} />
  );
};

const MapSelector = ({ 
  label = 'Select Location', 
  onLocationChange, 
  initialPosition = [51.505, -0.09], // London default
  error,
  required = false,
  height = '300px'
}) => {
  const [position, setPosition] = useState(null);
  const [address, setAddress] = useState('');

  useEffect(() => {
    if (position) {
      // Reverse geocoding to get address
      fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.lat}&lon=${position.lng}`)
        .then(response => response.json())
        .then(data => {
          const displayName = data.display_name || `${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`;
          setAddress(displayName);
          onLocationChange({
            lat: position.lat,
            lng: position.lng,
            address: displayName
          });
        })
        .catch(() => {
          const coords = `${position.lat.toFixed(6)}, ${position.lng.toFixed(6)}`;
          setAddress(coords);
          onLocationChange({
            lat: position.lat,
            lng: position.lng,
            address: coords
          });
        });
    }
  }, [position, onLocationChange]);

  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className={`border rounded-lg overflow-hidden ${error ? 'border-red-300' : 'border-gray-300'}`}>
        <MapContainer
          center={initialPosition}
          zoom={13}
          style={{ height, width: '100%' }}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          <LocationMarker position={position} setPosition={setPosition} />
        </MapContainer>
      </div>

      {address && (
        <div className="p-3 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-700">
            <span className="font-medium">Selected Location:</span> {address}
          </p>
        </div>
      )}

      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
      
      <p className="text-xs text-gray-500">
        Click on the map to select a location
      </p>
    </div>
  );
};

export default MapSelector;
