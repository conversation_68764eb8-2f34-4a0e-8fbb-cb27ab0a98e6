// API Endpoints
export const API_ENDPOINTS = {
  SUBMIT_FORM: '/submit-form',
  SEND_OTP: '/send-otp',
  VERIFY_OTP: '/verify-otp',
  UPLOAD_FILE: '/upload',
};

// File Upload Constants
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_FILES: 5,
  ACCEPTED_TYPES: {
    'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp'],
    'video/*': ['.mp4', '.mov', '.avi', '.wmv', '.flv']
  }
};

// Form Validation
export const VALIDATION = {
  PHONE_REGEX: /^\+?[\d\s-()]+$/,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  MAX_MESSAGE_LENGTH: 500,
  OTP_LENGTH: 6
};

// Map Configuration
export const MAP_CONFIG = {
  DEFAULT_CENTER: [51.505, -0.09], // London
  DEFAULT_ZOOM: 13,
  TILE_URL: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
  ATTRIBUTION: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
};

// Toast Configuration
export const TOAST_CONFIG = {
  position: 'top-right',
  autoClose: 5000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
};

// PWA Configuration
export const PWA_CONFIG = {
  CACHE_NAME: 'landing-form-v1',
  OFFLINE_URL: '/offline.html'
};
