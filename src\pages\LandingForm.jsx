import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import ReCAPTCHA from 'react-google-recaptcha';
import { toast } from 'react-toastify';
import { Button, Input } from '../components/ui';
import { DatePicker, FileUpload, MapSelector } from '../components/form';
import { submitFormData, sendOTP, verifyOTP } from '../services/api';

const schema = yup.object({
  firstName: yup.string().required('First name is required'),
  lastName: yup.string().required('Last name is required'),
  email: yup.string().email('Invalid email').required('Email is required'),
  phone: yup.string().matches(/^\+?[\d\s-()]+$/, 'Invalid phone number'),
  selectedDate: yup.date().required('Date selection is required'),
  location: yup.object().required('Location selection is required'),
  message: yup.string().max(500, 'Message must be less than 500 characters')
});

const LandingForm = () => {
  const [files, setFiles] = useState([]);
  const [captchaValue, setCaptchaValue] = useState(null);
  const [showOTPField, setShowOTPField] = useState(false);
  const [otpValue, setOtpValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);

  const { register, handleSubmit, formState: { errors }, setValue, watch } = useForm({
    resolver: yupResolver(schema)
  });

  const phoneValue = watch('phone');

  const handleLocationChange = (location) => {
    setSelectedLocation(location);
    setValue('location', location);
  };

  const handleFilesChange = (newFiles) => {
    setFiles(newFiles);
  };

  const handleSendOTP = async () => {
    if (!phoneValue) {
      toast.error('Please enter a phone number first');
      return;
    }

    try {
      await sendOTP(phoneValue);
      setShowOTPField(true);
      toast.success('OTP sent to your phone number');
    } catch (error) {
      toast.error('Failed to send OTP. Please try again.');
    }
  };

  const onSubmit = async (data) => {
    if (!captchaValue) {
      toast.error('Please complete the captcha');
      return;
    }

    if (phoneValue && !showOTPField) {
      toast.error('Please verify your phone number with OTP');
      return;
    }

    if (phoneValue && showOTPField && !otpValue) {
      toast.error('Please enter the OTP');
      return;
    }

    setIsSubmitting(true);

    try {
      // Verify OTP if phone number is provided
      if (phoneValue && otpValue) {
        const otpValid = await verifyOTP(phoneValue, otpValue);
        if (!otpValid) {
          toast.error('Invalid OTP. Please try again.');
          setIsSubmitting(false);
          return;
        }
      }

      // Prepare form data
      const formData = new FormData();
      Object.keys(data).forEach(key => {
        if (key === 'location') {
          formData.append(key, JSON.stringify(data[key]));
        } else {
          formData.append(key, data[key]);
        }
      });

      // Add files
      files.forEach((fileObj, index) => {
        formData.append(`file_${index}`, fileObj.file);
      });

      formData.append('captcha', captchaValue);
      if (otpValue) {
        formData.append('otp', otpValue);
      }

      await submitFormData(formData);
      toast.success('Form submitted successfully!');
      
      // Reset form
      window.location.reload();
    } catch (error) {
      toast.error('Failed to submit form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white shadow-xl rounded-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Contact Form</h1>
            <p className="mt-2 text-gray-600">Please fill out all the required information</p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input
                label="First Name"
                {...register('firstName')}
                error={errors.firstName?.message}
                required
              />
              <Input
                label="Last Name"
                {...register('lastName')}
                error={errors.lastName?.message}
                required
              />
            </div>

            <Input
              label="Email"
              type="email"
              {...register('email')}
              error={errors.email?.message}
              required
            />

            <div className="space-y-2">
              <Input
                label="Phone Number (Optional)"
                type="tel"
                {...register('phone')}
                error={errors.phone?.message}
                placeholder="+****************"
              />
              {phoneValue && !showOTPField && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleSendOTP}
                >
                  Send OTP
                </Button>
              )}
              {showOTPField && (
                <Input
                  label="Enter OTP"
                  value={otpValue}
                  onChange={(e) => setOtpValue(e.target.value)}
                  placeholder="Enter 6-digit OTP"
                  maxLength={6}
                />
              )}
            </div>

            <DatePicker
              label="Select Date"
              selected={watch('selectedDate')}
              onChange={(date) => setValue('selectedDate', date)}
              error={errors.selectedDate?.message}
              required
              minDate={new Date()}
            />

            <MapSelector
              label="Select Location"
              onLocationChange={handleLocationChange}
              error={errors.location?.message}
              required
            />

            <FileUpload
              label="Upload Images/Videos (Optional)"
              onFilesChange={handleFilesChange}
              maxFiles={5}
            />

            <div className="space-y-1">
              <label className="block text-sm font-medium text-gray-700">
                Message (Optional)
              </label>
              <textarea
                {...register('message')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Additional message..."
              />
              {errors.message && (
                <p className="text-sm text-red-600">{errors.message.message}</p>
              )}
            </div>

            <div className="flex justify-center">
              <ReCAPTCHA
                sitekey={import.meta.env.VITE_RECAPTCHA_SITE_KEY}
                onChange={setCaptchaValue}
              />
            </div>

            <Button
              type="submit"
              size="lg"
              className="w-full"
              loading={isSubmitting}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Submitting...' : 'Submit Form'}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LandingForm;
