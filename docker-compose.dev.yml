version: '3.8'

services:
  # Frontend React App (Development)
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5173:5173"
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:3001/api
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - backend-dev
      - redis-dev
    networks:
      - dev-network
    restart: unless-stopped

  # Backend API (Development)
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=************************************************/landing_form_dev
      - REDIS_URL=redis://redis-dev:6379
      - JWT_SECRET=dev_jwt_secret_key
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASS=${SMTP_PASS}
    depends_on:
      - postgres-dev
      - redis-dev
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./uploads:/app/uploads
    networks:
      - dev-network
    restart: unless-stopped

  # PostgreSQL Database (Development)
  postgres-dev:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=landing_form_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    networks:
      - dev-network
    restart: unless-stopped

  # Redis for OTP storage (Development)
  redis-dev:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - dev-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Adminer for database management
  adminer:
    image: adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres-dev
    networks:
      - dev-network
    restart: unless-stopped

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis-dev
    networks:
      - dev-network
    restart: unless-stopped

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  dev-network:
    driver: bridge
