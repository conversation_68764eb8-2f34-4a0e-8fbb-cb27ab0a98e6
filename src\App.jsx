import React from "react";
import { <PERSON>rowserRouter as Router, Routes, Route } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { LandingForm } from "./pages";
import { TOAST_CONFIG } from "./utils/constants";

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<LandingForm />} />
          <Route path="/form" element={<LandingForm />} />
        </Routes>

        <ToastContainer {...TOAST_CONFIG} />
      </div>
    </Router>
  );
}

export default App;
